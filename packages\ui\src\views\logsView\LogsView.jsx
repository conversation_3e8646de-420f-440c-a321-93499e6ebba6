import { useState, useEffect } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Grid,
  Chip,
  Pagination,
  Tooltip
} from '@mui/material'
import { Search, Refresh } from '@mui/icons-material'
import logsAPI from '../../api/logs'

const actionTypeOptions = [
  { value: '', label: 'Tất cả' },
  { value: 'create', label: 'Thêm mới' },
  { value: 'update', label: 'Sửa' },
  { value: 'delete', label: 'Xóa' }
]

const targetTypeOptions = [
  { value: '', label: 'Tất cả' },
  { value: 'agent', label: 'Agent' },
  { value: 'folder', label: 'Folder' },
  { value: 'file', label: 'File' }
]

const pageSizeOptions = [15, 30, 50, 100]

const LogsView = () => {
  const [logs, setLogs] = useState([])
  const [loading, setLoading] = useState(false)
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 15,
    totalCount: 0,
    totalPages: 0
  })
  const [filters, setFilters] = useState({
    dateType: 'range', // 'range' | 'single'
    date: '', // single date
    fromDate: '',
    toDate: '',
    target_type: '',
    username: '',
    action: '',
    content: ''
  })

  // Fetch logs
  const fetchLogs = async (params = {}) => {
    setLoading(true)
    try {
      const query = {
        page: pagination.page,
        pageSize: pagination.pageSize,
        ...filters
      }
      // Xử lý filter ngày
      if (filters.dateType === 'single' && filters.date) {
        query.fromDate = filters.date
        query.toDate = filters.date
      } else if (filters.dateType === 'range') {
        if (filters.fromDate) query.fromDate = filters.fromDate
        if (filters.toDate) query.toDate = filters.toDate
      }
      delete query.dateType
      delete query.date
      const response = await logsAPI.getAllLogs(query)
      setLogs(response.data)
      setPagination((prev) => ({ ...prev, ...response.pagination }))
    } catch (error) {
      console.error('Error fetching logs:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchLogs()
    // eslint-disable-next-line
  }, [pagination.page, pagination.pageSize])

  // Filter logic
  const handleFilterChange = (field, value) => {
    setFilters((prev) => ({ ...prev, [field]: value }))
  }

  const handleSearch = () => {
    setPagination((prev) => ({ ...prev, page: 1 }))
    fetchLogs()
  }

  const handleReset = () => {
    const defaultFilters = {
      dateType: 'range',
      date: '',
      fromDate: '',
      toDate: '',
      target_type: '',
      username: '',
      action: '',
      content: ''
    }
    setFilters(defaultFilters)
    setPagination((prev) => ({ ...prev, page: 1 }))
    // Tự động fetch lại data sau khi reset
    setTimeout(() => {
      fetchLogs()
    }, 100)
  }

  const handlePageChange = (event, newPage) => {
    setPagination((prev) => ({ ...prev, page: newPage }))
  }

  const handlePageSizeChange = (event) => {
    setPagination((prev) => ({ ...prev, pageSize: parseInt(event.target.value, 10), page: 1 }))
  }

  // Mapping action to label
  const getActionLabel = (action) => {
    switch (action) {
      case 'create':
        return 'Thêm mới'
      case 'update':
        return 'Sửa'
      case 'delete':
        return 'Xóa'
      default:
        return action
    }
  }

  // Mapping target_type to label
  const getTargetTypeLabel = (type) => {
    switch (type) {
      case 'agent':
        return 'Agentflow'
      case 'folder':
        return 'Folder'
      case 'file':
        return 'File'
      default:
        return type
    }
  }

  // Format date theo yêu cầu: yyyy/mm/dd hh:mm:ss
  const formatDate = (dateString) => {
    if (!dateString) return ''
    const d = new Date(dateString)
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')
    const seconds = String(d.getSeconds()).padStart(2, '0')
    return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`
  }

  // Format nội dung thay đổi
  const formatChanges = (changes) => {
    if (!changes) return 'Không có thông tin'
    if (typeof changes === 'string') return changes
    if (typeof changes === 'object') {
      try {
        return JSON.stringify(changes, null, 2)
      } catch (e) {
        return 'Dữ liệu không hợp lệ'
      }
    }
    return String(changes)
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant='h2' gutterBottom>
        Quản lý lịch sử thao tác Agentflows/thư viện
      </Typography>
      {/* Bộ lọc */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems='center'>
            {/* Thời gian */}
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Kiểu thời gian</InputLabel>
                <Select value={filters.dateType} label='Kiểu thời gian' onChange={(e) => handleFilterChange('dateType', e.target.value)}>
                  <MenuItem value='range'>Khoảng thời gian</MenuItem>
                  <MenuItem value='single'>Một ngày</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            {filters.dateType === 'single' ? (
              <Grid item xs={12} md={2}>
                <TextField
                  fullWidth
                  label='Ngày'
                  type='date'
                  value={filters.date}
                  onChange={(e) => handleFilterChange('date', e.target.value)}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
            ) : (
              <>
                <Grid item xs={12} md={2}>
                  <TextField
                    fullWidth
                    label='Từ ngày'
                    type='date'
                    value={filters.fromDate}
                    onChange={(e) => handleFilterChange('fromDate', e.target.value)}
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={12} md={2}>
                  <TextField
                    fullWidth
                    label='Đến ngày'
                    type='date'
                    value={filters.toDate}
                    onChange={(e) => handleFilterChange('toDate', e.target.value)}
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
              </>
            )}
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                label='Tìm theo tên tài liệu, nội dung thay đổi'
                value={filters.content}
                onChange={(e) => handleFilterChange('content', e.target.value)}
                placeholder='Tìm theo tên tài liệu, nội dung thay đổi'
              />
            </Grid>
            {/* Loại tài liệu */}
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Loại tài liệu</InputLabel>
                <Select
                  value={filters.target_type}
                  label='Loại tài liệu'
                  onChange={(e) => handleFilterChange('target_type', e.target.value)}
                >
                  {targetTypeOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            {/* Thao tác */}
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Thao tác</InputLabel>
                <Select
                  value={filters.action}
                  label='Thao tác'
                  onChange={(e) => handleFilterChange('action', e.target.value)}
                  renderValue={(selected) => {
                    const found = actionTypeOptions.find((opt) => opt.value === selected)
                    return found ? found.label : ''
                  }}
                >
                  {actionTypeOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button variant='outlined' startIcon={<Refresh />} onClick={handleReset} disabled={loading}>
                Xóa lọc
              </Button>
            </Grid>
            {/* Button */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 2 }}>
                <Button variant='contained' startIcon={<Search />} onClick={handleSearch} disabled={loading}>
                  Tìm kiếm
                </Button>
                <Button variant='outlined' startIcon={<Refresh />} onClick={handleReset} disabled={loading}>
                  Xóa lọc
                </Button>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      {/* Listing */}
      <Card>
        <CardContent>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>STT</TableCell>
                  <TableCell>Thời gian</TableCell>
                  <TableCell>Người dùng</TableCell>
                  <TableCell>Thao tác</TableCell>
                  <TableCell>Loại tài liệu</TableCell>
                  <TableCell>Tên tài liệu</TableCell>
                  <TableCell>Nội dung thay đổi</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} align='center' sx={{ py: 4 }}>
                      <Typography>Đang tải dữ liệu...</Typography>
                    </TableCell>
                  </TableRow>
                ) : logs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align='center' sx={{ py: 4 }}>
                      <Typography color='textSecondary'>Không có dữ liệu</Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  logs.map((log) => (
                    <TableRow key={log.id} hover>
                      <TableCell>{log.id}</TableCell>
                      <TableCell>{formatDate(log.created_at)}</TableCell>
                      <TableCell>{log.username}</TableCell>
                      <TableCell>
                        <Chip
                          label={getActionLabel(log.action)}
                          color={
                            log.action === 'CREATE_AGENT'
                              ? 'success'
                              : log.action === 'UPDATE_AGENT'
                              ? 'warning'
                              : log.action === 'DELETE_AGENT'
                              ? 'error'
                              : 'default'
                          }
                          size='small'
                        />
                      </TableCell>
                      <TableCell>
                        <Chip label={getTargetTypeLabel(log.target_type)} variant='outlined' size='small' />
                      </TableCell>
                      <TableCell>{log.target_name}</TableCell>
                      <TableCell>
                        <Box sx={{ maxWidth: 300, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                          <Tooltip title={formatChanges(log.changes)} placement='top'>
                            <pre style={{ whiteSpace: 'pre-wrap', fontSize: '12px', margin: 0, cursor: 'help' }}>
                              {formatChanges(log.changes).length > 100
                                ? formatChanges(log.changes).substring(0, 100) + '...'
                                : formatChanges(log.changes)}
                            </pre>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          {/* Pagination + Page size */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2, flexWrap: 'wrap', gap: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Typography variant='body2' color='textSecondary'>
                Hiển thị {(pagination.page - 1) * pagination.pageSize + 1} -{' '}
                {Math.min(pagination.page * pagination.pageSize, pagination.totalCount)} của {pagination.totalCount} bản ghi
              </Typography>
              <FormControl sx={{ minWidth: 120 }}>
                <InputLabel size='small'>Số bản ghi</InputLabel>
                <Select value={pagination.pageSize} onChange={handlePageSizeChange} size='small' label='Số bản ghi'>
                  {pageSizeOptions.map((size) => (
                    <MenuItem key={size} value={size}>
                      {size}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Button
                variant='outlined'
                size='small'
                disabled={pagination.page <= 1}
                onClick={() => handlePageChange(null, pagination.page - 1)}
              >
                Trước
              </Button>
              <Pagination
                count={pagination.totalPages}
                page={pagination.page}
                onChange={handlePageChange}
                color='primary'
                showFirstButton
                showLastButton
                siblingCount={1}
                boundaryCount={1}
              />
              <Button
                variant='outlined'
                size='small'
                disabled={pagination.page >= pagination.totalPages}
                onClick={() => handlePageChange(null, pagination.page + 1)}
              >
                Sau
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Box>
  )
}

export default LogsView
