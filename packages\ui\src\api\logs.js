import axios from 'axios'

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:3000'

const logsAPI = {
  // L<PERSON>y tất cả logs với filter
  getAllLogs: async (params = {}) => {
    const response = await axios.get(`${API_BASE_URL}/api/v1/logs`, {
      params,
      withCredentials: true
    })
    return response.data
  },

  // <PERSON><PERSON><PERSON> logs theo agent ID
  getLogsByAgent: async (agentId, params = {}) => {
    const response = await axios.get(`${API_BASE_URL}/api/v1/logs/agent/${agentId}`, {
      params,
      withCredentials: true
    })
    return response.data
  }
}

export default logsAPI 