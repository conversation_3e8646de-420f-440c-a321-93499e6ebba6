import { lazy } from 'react'

import Loadable from '@/ui-component/loading/Loadable'

// project imports
import MainLayout from '@/layout/MainLayout'
const AdminAccount = Loadable(lazy(() => import('@/views/AdminAccount')))
const Profile = Loadable(lazy(() => import('@/views/profile')))
const LogsView = Loadable(lazy(() => import('@/views/logs/LogsView')))

// canvas routing

// ==============================|| CANVAS ROUTING ||============================== //

const AdminRoutes = {
  path: '/',
  element: <MainLayout />,
  children: [
    {
      path: '/admin-account',
      element: <AdminAccount />
    },
    {
      path: '/profile-group/:id',
      element: <Profile />
    },
    {
      path: '/profile/:id',
      element: <Profile />
    },
    {
      path: '/logs',
      element: <LogsView />
    }
  ]
}

export default AdminRoutes
